"""
使用AkShare获取港股人民币计价数据
专门获取港股通及港股数据，并转换为人民币计价
文件名：akshare_hk_rmb.py
"""

import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
import time

# A+H股对应关系
AH_PAIRS = {
    '000001': {'h_code': '00001', 'name': '平安银行'},
    '000002': {'h_code': '00002', 'name': '万科A'}, 
    '600036': {'h_code': '00023', 'name': '招商银行'},
    '600000': {'h_code': '00601', 'name': '浦发银行'},
    '600028': {'h_code': '00857', 'name': '中国石化'},
    '601318': {'h_code': '02318', 'name': '中国平安'},
    '601328': {'h_code': '03328', 'name': '交通银行'},
    '601398': {'h_code': '01398', 'name': '工商银行'},
    '601988': {'h_code': '03988', 'name': '中国银行'},
    '601628': {'h_code': '02628', 'name': '中国人寿'},
}

def check_akshare():
    """检查AkShare是否安装"""
    try:
        import akshare as ak
        print("✅ AkShare已安装，版本:", ak.__version__)
        return True
    except ImportError:
        print("❌ AkShare未安装，请运行: pip install akshare")
        return False

def get_hkd_to_rmb_rate():
    """获取港币对人民币实时汇率"""
    try:
        # 使用AkShare获取汇率数据
        fx_df = ak.currency_convert(
            from_currency="HKD",
            to_currency="CNY",
            amount=1
        )
        if fx_df is not None and not fx_df.empty:
            rate = float(fx_df['result'])
            print(f"💱 当前汇率: 1港币 = {rate:.4f}人民币")
            return rate
    except:
        pass
    
    # 如果获取失败，使用默认汇率
    default_rate = 0.92
    print(f"💱 使用默认汇率: 1港币 = {default_rate}人民币")
    return default_rate

def get_hk_stock_data_akshare(symbol, period="daily", start_date=None, end_date=None):
    """
    使用AkShare获取港股数据
    symbol: 港股代码 (如: 00001)
    period: 数据周期 (daily)
    """
    try:
        print(f"    使用AkShare获取港股 {symbol} 数据...")
        
        # 确保代码格式正确 (5位数字)
        symbol = symbol.zfill(5)
        
        # 计算日期范围
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        # 使用AkShare获取港股历史数据
        df = ak.stock_hk_hist(
            symbol=symbol,
            period=period,
            start_date=start_date,
            end_date=end_date,
            adjust="qfq"  # 前复权
        )
        
        if df is not None and not df.empty:
            print(f"    ✅ 成功获取 {len(df)} 条港股数据")
            return df
        else:
            print(f"    ❌ 未获取到港股数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"    ❌ AkShare港股数据获取失败: {e}")
        return pd.DataFrame()

def get_hk_stock_realtime_akshare(symbol):
    """获取港股实时数据"""
    try:
        print(f"    获取港股 {symbol} 实时数据...")
        symbol = symbol.zfill(5)
        
        # 获取实时行情
        df = ak.stock_hk_spot_em()
        
        if df is not None and not df.empty:
            # 筛选指定股票
            stock_data = df[df['代码'] == symbol]
            if not stock_data.empty:
                row = stock_data.iloc[0]
                
                # 构造标准格式数据
                result_df = pd.DataFrame([{
                    '日期': datetime.now().strftime('%Y-%m-%d'),
                    '开盘': float(row['今开']),
                    '收盘': float(row['最新价']),
                    '最高': float(row['最高']),
                    '最低': float(row['最低']),
                    '昨收': float(row['昨收']),
                    '成交量': int(row['成交量']) if '成交量' in row else 0
                }])
                
                print(f"    ✅ 成功获取实时数据")
                return result_df
        
        print(f"    ❌ 未找到股票 {symbol} 的实时数据")
        return pd.DataFrame()
        
    except Exception as e:
        print(f"    ❌ 实时数据获取失败: {e}")
        return pd.DataFrame()

def convert_hkd_to_rmb_dataframe(df, exchange_rate):
    """将港股DataFrame的港币价格转换为人民币"""
    if df.empty:
        return df
    
    df_rmb = df.copy()
    
    # 价格字段转换
    price_columns = ['开盘', '收盘', '最高', '最低', '昨收']
    
    for col in price_columns:
        if col in df_rmb.columns:
            df_rmb[col] = (df_rmb[col] * exchange_rate).round(2)
    
    return df_rmb

def get_hk_stock_rmb_data(h_code, days=30):
    """获取港股人民币计价数据的完整流程"""
    print(f"📈 获取港股 {h_code} 人民币计价数据")
    
    # 获取汇率
    exchange_rate = get_hkd_to_rmb_rate()
    
    # 计算日期范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=days*2)).strftime('%Y%m%d')
    
    # 方法1: 尝试获取历史数据
    df_hist = get_hk_stock_data_akshare(
        symbol=h_code,
        start_date=start_date,
        end_date=end_date
    )
    
    if not df_hist.empty:
        # 转换为人民币
        df_rmb = convert_hkd_to_rmb_dataframe(df_hist, exchange_rate)
        
        # 标准化列名和格式
        result_data = []
        for _, row in df_rmb.tail(days).iterrows():  # 取最近的数据
            try:
                result_data.append({
                    '交易日期': row['日期'],
                    '开盘价': float(row['开盘']),
                    '最高价': float(row['最高']),
                    '最低价': float(row['最低']),
                    '收盘价': float(row['收盘']),
                    '前收盘价': float(row['昨收'])
                })
            except Exception as e:
                continue
        
        if result_data:
            return result_data
    
    # 方法2: 如果历史数据获取失败，尝试实时数据
    print(f"    历史数据获取失败，尝试实时数据...")
    df_realtime = get_hk_stock_realtime_akshare(h_code)
    
    if not df_realtime.empty:
        df_rmb = convert_hkd_to_rmb_dataframe(df_realtime, exchange_rate)
        
        result_data = []
        for _, row in df_rmb.iterrows():
            try:
                result_data.append({
                    '交易日期': row['日期'],
                    '开盘价': float(row['开盘']),
                    '最高价': float(row['最高']),
                    '最低价': float(row['最低']),
                    '收盘价': float(row['收盘']),
                    '前收盘价': float(row['昨收'])
                })
            except Exception as e:
                continue
        
        return result_data
    
    print(f"    ❌ 所有方法都失败了")
    return []

def create_ah_rmb_dataset(stock_codes=None, days=30):
    """创建A+H股人民币计价数据集"""
    if not check_akshare():
        return pd.DataFrame()
    
    if stock_codes is None:
        stock_codes = list(AH_PAIRS.keys())
    
    all_data = []
    
    print(f"🚀 开始获取数据，目标天数: {days}")
    print("=" * 60)
    
    for a_code in stock_codes:
        if a_code not in AH_PAIRS:
            continue
            
        stock_info = AH_PAIRS[a_code]
        h_code = stock_info['h_code']
        name = stock_info['name']
        
        print(f"\n📊 处理 {name}")
        print(f"   A股代码: {a_code}")
        print(f"   港股代码: {h_code}")
        print("-" * 40)
        
        # 获取港股人民币数据
        hk_data = get_hk_stock_rmb_data(h_code, days)
        
        if hk_data:
            for record in hk_data:
                all_data.append({
                    '交易日期': record['交易日期'],
                    '股票代码': a_code,
                    '港股代码': h_code,
                    '开盘价': record['开盘价'],
                    '最高价': record['最高价'],
                    '最低价': record['最低价'],
                    '收盘价': record['收盘价'],
                    '前收盘价': record['前收盘价']
                })
            
            print(f"   ✅ 成功获取 {len(hk_data)} 条数据")
        else:
            print(f"   ❌ 获取失败")
        
        # 避免请求过频
        time.sleep(1)
    
    print("\n" + "=" * 60)
    df = pd.DataFrame(all_data)
    
    if not df.empty:
        print(f"🎉 总计获取 {len(df)} 条港股人民币数据")
        
        # 按交易日期降序排列
        df = df.sort_values('交易日期', ascending=False)
        
        # 数据统计
        print(f"📈 数据统计:")
        print(f"   涉及股票: {df['股票代码'].nunique()} 只")
        print(f"   时间范围: {df['交易日期'].min()} 至 {df['交易日期'].max()}")
        
    return df

def save_data_to_csv(df, filename=None):
    """保存数据到CSV"""
    if df.empty:
        print("❌ 没有数据可保存")
        return False
    
    if filename is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"港股人民币数据_AkShare_{timestamp}.csv"
    
    try:
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✅ 数据已保存: {filename}")
        return True
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def main():
    print("🏦 港股人民币计价数据获取工具 (AkShare版)")
    print("=" * 60)
    print("📋 功能特色:")
    print("   • 使用AkShare库获取港股数据")
    print("   • 自动获取实时汇率进行人民币转换")
    print("   • 支持历史数据和实时数据")
    print("   • 输出标准格式CSV文件")
    print("=" * 60)
    
    if not check_akshare():
        print("\n请先安装AkShare: pip install akshare")
        return
    
    print(f"\n📋 可选股票:")
    for i, (code, info) in enumerate(AH_PAIRS.items(), 1):
        print(f"{i:2d}. {info['name']} (A:{code} → H:{info['h_code']})")
    
    try:
        print(f"\n⚙️ 参数设置:")
        
        # 获取天数
        days_input = input("数据天数 (1-60，默认7): ").strip()
        days = int(days_input) if days_input.isdigit() and 1 <= int(days_input) <= 60 else 7
        
        # 选择股票
        choice_input = input(f"选择股票 (1-{len(AH_PAIRS)} 或 all，默认all): ").strip().lower()
        
        if choice_input.isdigit():
            choice = int(choice_input) - 1
            if 0 <= choice < len(AH_PAIRS):
                stock_codes = [list(AH_PAIRS.keys())[choice]]
                print(f"✅ 已选择: {AH_PAIRS[stock_codes[0]]['name']}")
            else:
                print("❌ 选择无效，获取全部股票")
                stock_codes = None
        else:
            stock_codes = None
            print("✅ 已选择: 全部股票")
        
        print(f"\n🚀 开始获取港股人民币数据...")
        
        # 获取数据
        df = create_ah_rmb_dataset(stock_codes, days)
        
        if not df.empty:
            print(f"\n📋 数据预览:")
            print(df.head(10).to_string(index=False))
            
            # 保存数据
            save_data_to_csv(df)
            
            # 显示价格统计
            print(f"\n💰 最新价格 (人民币):")
            latest_prices = df.groupby(['股票代码', '港股代码']).first().reset_index()
            for _, row in latest_prices.iterrows():
                stock_name = AH_PAIRS[row['股票代码']]['name']
                print(f"   {stock_name}: {row['收盘价']:.2f} 元")
                
        else:
            print(f"\n❌ 未获取到任何数据")
            print("🔧 可能的解决方案:")
            print("   • 检查网络连接")
            print("   • 确认AkShare库为最新版本")
            print("   • 尝试在交易时间运行")
            print("   • 减少获取天数重试")
            
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        print("🔧 建议:")
        print("   • 检查AkShare安装: pip install --upgrade akshare")
        print("   • 检查网络连接")
    
    print(f"\n程序结束 🎉")

if __name__ == "__main__":
    main()